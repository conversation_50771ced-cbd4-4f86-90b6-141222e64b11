package com.trinasolar.gateway.security.service;

import com.trinasolar.common.security.service.AuthenticationService;
import reactor.core.publisher.Mono;

/**
 * WebFlux环境认证服务接口
 * <p>
 * 扩展基础认证服务，添加响应式支持
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface WebFluxAuthenticationService extends AuthenticationService {

    /**
     * 验证token并构建SecurityContext (异步版本 - webflux环境)
     * 
     * @param authorization Authorization头
     * @return 验证结果的Mono
     */
    Mono<Boolean> verifyTokenAndSetContextAsync(String authorization);
}
