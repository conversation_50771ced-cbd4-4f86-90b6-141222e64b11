package com.trinasolar.gateway.security.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.properties.SecurityProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全响应工具类 (WebFlux版本)
 * <p>
 * 提供统一的错误响应处理，支持webflux环境
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
public class SecurityResponseUtils {

    /**
     * 处理未授权响应 (WebFlux环境)
     * 
     * @param securityProperties 安全配置
     * @param objectMapper JSON映射器
     * @param response 响应对象
     * @param message 错误消息
     * @return Mono<Void>
     */
    public static Mono<Void> handleUnauthorized(SecurityProperties securityProperties,
                                               ObjectMapper objectMapper,
                                               ServerHttpResponse response,
                                               String message) {
        try {
            String authorizeUrl = buildAuthorizeUrl(securityProperties);
            Map<String, Object> result = buildErrorResponse(message, authorizeUrl);
            
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            
            String jsonResponse = objectMapper.writeValueAsString(result);
            return response.writeWith(Mono.just(response.bufferFactory()
                    .wrap(jsonResponse.getBytes(StandardCharsets.UTF_8))));
                    
        } catch (Exception e) {
            log.error("Error writing unauthorized response", e);
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            return response.setComplete();
        }
    }

    /**
     * 构建授权URL
     */
    private static String buildAuthorizeUrl(SecurityProperties securityProperties) {
        return securityProperties.getEnvUrl() + 
               SecurityConstant.AUTHORIZE_PATH + 
               securityProperties.getClientId() + 
               SecurityConstant.REDIRECT_URI + 
               securityProperties.getRedirectUrl();
    }

    /**
     * 构建错误响应
     */
    private static Map<String, Object> buildErrorResponse(String message, String authorizeUrl) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", message);
        result.put("data", authorizeUrl);
        return result;
    }
}
