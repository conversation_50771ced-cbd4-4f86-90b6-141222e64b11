package com.trinasolar.gateway.security.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.filter.AbstractSecurityFilter;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.service.WebFluxAuthenticationService;
import com.trinasolar.gateway.security.util.SecurityUtils;
import com.trinasolar.gateway.security.util.SecurityResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 网关Web安全过滤器 - WebFilter实现
 * <p>
 * 基于统一认证服务的WebFlux响应式安全过滤器
 * 只处理认证，不处理API权限
 * <p>
 * 此过滤器能够拦截所有WebFlux请求，包括网关本身的RestController接口
 * 与 GatewaySecurityFilter 配合使用，确保完整的安全覆盖
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
public class GatewayWebSecurityFilter implements WebFilter, Ordered {

    private final ObjectMapper objectMapper;
    private final AbstractSecurityFilter securityFilterHelper;

    public GatewayWebSecurityFilter(ObjectMapper objectMapper,
                                   SecurityProperties securityProperties,
                                   WebFluxAuthenticationService authenticationService) {
        this.objectMapper = objectMapper;
        this.securityFilterHelper = new AbstractSecurityFilter(securityProperties, authenticationService) {};
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();
        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        securityFilterHelper.logAuthenticationProcessing(path, authorization);

        // 1. 检查是否已经通过认证
        if (securityFilterHelper.isAlreadyAuthenticated()) {
            securityFilterHelper.logAuthenticationSkipped("already authenticated", path);
            return chain.filter(exchange);
        }

        // 2. 检查是否启用安全功能
        if (!securityFilterHelper.isSecurityEnabled()) {
            securityFilterHelper.logAuthenticationSkipped("security disabled", path);
            return chain.filter(exchange);
        }

        // 3. 默认白名单处理
        if (securityFilterHelper.matchDefaultWhiteList(path, null)) {
            securityFilterHelper.logAuthenticationSkipped("default whitelist", path);
            return chain.filter(exchange);
        }

        // 4. 配置白名单处理
        if (securityFilterHelper.matchConfiguredWhiteList(path, null)) {
            securityFilterHelper.logAuthenticationSkipped("configured whitelist", path);
            return chain.filter(exchange);
        }

        // 5. 内部系统签名验证
        String tsltoken = SecurityUtils.getValueFromRequest(request, "tsltoken");
        if (securityFilterHelper.matchInternalSystemSignature(tsltoken)) {
            securityFilterHelper.logAuthenticationSkipped("internal system signature", path);
            return chain.filter(exchange);
        }

        // 6. 检查Authorization头
        if (!securityFilterHelper.isValidAuthorizationHeader(authorization)) {
            log.warn("Missing or invalid authorization header for path: {}", path);
            return SecurityResponseUtils.handleUnauthorized(securityFilterHelper.securityProperties, objectMapper, response, "token 为空");
        }

        // 7. 验证token并设置安全上下文
        return ((WebFluxAuthenticationService)securityFilterHelper.authenticationService).verifyTokenAndSetContextAsync(authorization)
                .flatMap(isValid -> {
                    if (isValid) {
                        securityFilterHelper.logAuthenticationSuccess(path);
                        return chain.filter(exchange);
                    } else {
                        securityFilterHelper.logAuthenticationFailure(path);
                        return SecurityResponseUtils.handleUnauthorized(securityFilterHelper.securityProperties, objectMapper, response, "token 无效，验证失败");
                    }
                })
                .onErrorResume(throwable -> {
                    securityFilterHelper.logAuthenticationError(path, throwable);
                    return chain.filter(exchange);
                });
    }

    @Override
    public int getOrder() {
        // 设置比GlobalFilter更高的优先级，确保先执行
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
