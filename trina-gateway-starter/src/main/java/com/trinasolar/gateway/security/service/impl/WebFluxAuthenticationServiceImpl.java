package com.trinasolar.gateway.security.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.AuthenticationService;
import com.trinasolar.gateway.security.service.WebFluxAuthenticationService;
import com.trinasolar.gateway.security.util.WebClientRetryUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * WebFlux环境认证服务实现
 * <p>
 * 基于WebClient的响应式实现
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebFluxAuthenticationServiceImpl implements WebFluxAuthenticationService {

    private final ObjectMapper objectMapper;
    private final SecurityProperties securityProperties;
    private final WebClient webClient;
    private final Cache<String, Object> iamCache;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean matchWhiteUrl(List<String> whiteUrls, String path, String contextPath) {
        if (whiteUrls == null || whiteUrls.isEmpty()) {
            return false;
        }

        for (String whiteUrl : whiteUrls) {
            if (pathMatcher.match(whiteUrl, path)) {
                log.debug("Request path {} matches whitelist pattern: {}", path, whiteUrl);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean verifyTokenAndSetContext(String authorization) {
        // WebFlux环境不支持同步，抛出异常提示使用异步方法
        throw new UnsupportedOperationException("WebFlux environment should use verifyTokenAndSetContextAsync method");
    }

    @Override
    public Mono<Boolean> verifyTokenAndSetContextAsync(String authorization) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查token缓存
            Object cachedResult = iamCache.getIfPresent(tokenCacheKey);
            if (cachedResult != null) {
                log.debug("Token found in cache, validation successful");
                return Mono.just(true);
            }

            // 缓存未命中，远程验证token
            return verifyTokenRemotelyAndSetContext(authorization, token, tokenCacheKey);

        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return Mono.just(false);
        }
    }

    @Override
    public SecurityContext buildSecurityContext(String userInfoJson, String token) {
        // WebFlux环境不支持同步，抛出异常提示使用异步方法
        throw new UnsupportedOperationException("WebFlux environment should use buildSecurityContextAsync method");
    }



    /**
     * 远程验证token并设置安全上下文
     */
    private Mono<Boolean> verifyTokenRemotelyAndSetContext(String authorization, String token, String tokenCacheKey) {
        String url = securityProperties.getEnvUrl() + SecurityConstant.USERINFO_PATH;

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(15))  // 设置超时
                .retryWhen(WebClientRetryUtils.createTokenVerifyRetry(3, Duration.ofMillis(500), url))
                .flatMap(body -> processTokenResponse(body, token, tokenCacheKey))
                .doOnError(throwable -> WebClientRetryUtils.logConnectionError(throwable, url, "token verification"))
                .onErrorReturn(false);
    }

    /**
     * 处理token验证响应
     */
    private Mono<Boolean> processTokenResponse(String body, String token, String tokenCacheKey) {
        try {
            IamUserInfo userInfo = objectMapper.readValue(body, IamUserInfo.class);
            log.debug("Remote get userInfo: {}", userInfo);

            return buildSecurityContextFromUserInfoAsync(userInfo, token)
                    .map(securityContext -> {
                        SecurityContextHolder.setContext(securityContext);
                        iamCache.put(tokenCacheKey, token);
                        return true;
                    })
                    .onErrorResume(e -> {
                        log.error("Error building security context", e);
                        return Mono.just(false);
                    });
        } catch (Exception e) {
            log.error("Error parsing user info response", e);
            return Mono.just(false);
        }
    }

    /**
     * 构建安全上下文（从IamUserInfo）- 异步版本
     */
    private Mono<SecurityContext> buildSecurityContextFromUserInfoAsync(IamUserInfo userInfo, String token) {
        return Mono.fromCallable(() -> {
            SecurityContext context = new SecurityContext();

            // 设置用户主体信息
            SecurityContext.UserPrincipal principal = new SecurityContext.UserPrincipal();
            principal.setUserId(userInfo.getUid());
            principal.setUsername(userInfo.getCn());
            principal.setDisplayName(userInfo.getDisplayname());
            principal.setEmail(userInfo.getEmail());
            principal.setDeptId(userInfo.getDepartmentnumber());
            principal.setEmployeeNumber(userInfo.getEmployeenumber());
            principal.setPostType(userInfo.getPosttype());
            principal.setLocation(userInfo.getLocation());
            principal.setFilled(true);
            context.setPrincipal(principal);

            // 设置认证信息
            SecurityContext.Authentication authentication = new SecurityContext.Authentication();
            authentication.setAccessToken(token);
            authentication.setTokenType("Bearer");
            authentication.setAuthenticated(true);
            authentication.setAuthenticatedAt(LocalDateTime.now());
            context.setAuthentication(authentication);
            return context;
        }).flatMap(context ->
                loadUserPermissionsFromSystem(token, userInfo.getUid())
                        .map(authorization -> {
                            context.setAuthorization(authorization);
                            return context;
                        })
        );
    }

    /**
     * 从系统加载用户权限信息
     */
    private Mono<SecurityContext.Authorization> loadUserPermissionsFromSystem(String token, String userId) {
        // 这里应该调用权限服务获取用户权限
        // 由于gateway-starter中没有权限功能，这里返回空的授权信息
        return Mono.fromCallable(() -> {
            SecurityContext.Authorization authorization = new SecurityContext.Authorization();
            authorization.setPermissionsLoadedAt(LocalDateTime.now());
            log.debug("Permissions loaded for user: {} (gateway mode - no permissions)", userId);
            return authorization;
        });
    }

    /**
     * 构建安全上下文（从IamUserInfo）- 同步版本
     */
    private SecurityContext buildSecurityContextFromUserInfo(IamUserInfo userInfo, String token) {
        SecurityContext context = new SecurityContext();

        // 设置用户主体信息
        SecurityContext.UserPrincipal principal = new SecurityContext.UserPrincipal();
        principal.setUserId(userInfo.getUid());
        principal.setUsername(userInfo.getCn());
        principal.setDisplayName(userInfo.getDisplayname());
        principal.setEmail(userInfo.getEmail());
        principal.setDeptId(userInfo.getDepartmentnumber());
        principal.setEmployeeNumber(userInfo.getEmployeenumber());
        principal.setPostType(userInfo.getPosttype());
        principal.setLocation(userInfo.getLocation());
        principal.setFilled(true);
        context.setPrincipal(principal);

        // 设置认证信息
        SecurityContext.Authentication authentication = new SecurityContext.Authentication();
        authentication.setAccessToken(token);
        authentication.setTokenType("Bearer");
        authentication.setAuthenticated(true);
        authentication.setAuthenticatedAt(LocalDateTime.now());
        context.setAuthentication(authentication);

        return context;
    }
}
