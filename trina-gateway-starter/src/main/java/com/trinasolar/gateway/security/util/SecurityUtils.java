package com.trinasolar.gateway.security.util;


import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.core.spring.SpringContextHolder;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.config.ApiPathInit;
import com.trinasolar.gateway.security.util.ErrorResponseUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-14 10:58
 **/
@Slf4j
@UtilityClass
public class SecurityUtils {

    /**
     * 处理权限不足错误
     *
     * @param objectMapper 对象映射器
     * @param response     响应
     * @param message      消息
     * @return {@link Mono }<{@link Void }>
     */
    public static Mono<Void> handleForbidden(ObjectMapper objectMapper, ServerHttpResponse response, String message) {
        return ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, message);
    }

    /**
     * 从请求参数或请求头中获取指定key的值（优先级：参数 > 头信息）
     * @param request 请求对象
     * @param key 要获取的键名
     * @return 匹配到的第一个值，未找到返回null
     */
    public static String getValueFromRequest(ServerHttpRequest request, String key) {
        // 优先从查询参数获取
        String paramValue = request.getQueryParams().getFirst(key);
        if (StringUtils.hasText(paramValue)) {
            return paramValue;
        }
        // 从请求头获取
        String headerValue = request.getHeaders().getFirst(key);
        if (StringUtils.hasText(headerValue)) {
            return headerValue;
        }

        return null;
    }
}
