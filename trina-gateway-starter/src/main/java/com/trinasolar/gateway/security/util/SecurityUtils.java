package com.trinasolar.gateway.security.util;


import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.core.spring.SpringContextHolder;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.config.ApiPathInit;
import com.trinasolar.gateway.security.util.ErrorResponseUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-14 10:58
 **/
@Slf4j
@UtilityClass
public class SecurityUtils {

    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    // 通用白名单匹配方法
    public static boolean matchWhiteUrl(List<String> whiteUrls, String path) {
        log.debug("Matching path: {} against whitelist: {}", path, whiteUrls);
        if (whiteUrls == null || whiteUrls.isEmpty()) {
            return false;
        }
        return whiteUrls.stream()
                .anyMatch(pattern -> {
                    boolean match = PATH_MATCHER.match(pattern, path);
                    if (match) {
                        log.debug("Path {} matched whitelist pattern: {}", path, pattern);
                    }
                    return match;
                });
    }

    // 新增token验证方法
    public static Mono<Boolean> verifyTokenAndSetContext(String authorization, Cache<String, Object> iamCache, WebClient webClient,
                                                         ObjectMapper objectMapper, SecurityProperties securityProperties) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查token缓存
            Object cachedResult = iamCache.getIfPresent(tokenCacheKey);
            if (cachedResult != null) {
                log.debug("Token found in cache, validation successful");
                ApiPathInit apiPathInit = SpringUtil.getBean(ApiPathInit.class);
                apiPathInit.init();
                return Mono.just(true);
            }

            // 缓存未命中，远程验证token
            return verifyTokenRemotelyAndSetContext(authorization, token, tokenCacheKey, iamCache, webClient, objectMapper, securityProperties);

        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return Mono.just(false);
        }
    }

    private static Mono<Boolean> verifyTokenRemotelyAndSetContext(String authorization, String token, String tokenCacheKey, Cache<String, Object> iamCache,
                                                                  WebClient webClient, ObjectMapper objectMapper, SecurityProperties securityProperties) {
        String url = securityProperties.getEnvUrl() + SecurityConstant.USERINFO_PATH;

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(15))  // 设置超时
                .retryWhen(WebClientRetryUtils.createTokenVerifyRetry(3, Duration.ofMillis(500), url))
                .flatMap(body -> processTokenResponse(body, token, tokenCacheKey, iamCache, objectMapper, webClient, securityProperties))
                .doOnError(throwable -> WebClientRetryUtils.logConnectionError(throwable, url, "token verification"))
                .onErrorReturn(false);
    }

    private static Mono<Boolean> processTokenResponse(String body, String token, String tokenCacheKey,
                                                      Cache<String, Object> iamCache, ObjectMapper objectMapper, WebClient webClient, SecurityProperties securityProperties) {
        try {
            IamUserInfo userInfo = objectMapper.readValue(body, IamUserInfo.class);
            log.debug("Remote get userInfo: {}", userInfo);

            return buildSecurityContextFromUserInfo(userInfo, token, webClient, securityProperties)
                    .map(securityContext -> {
                        SecurityContextHolder.setContext(securityContext);
                        iamCache.put(tokenCacheKey, token);
                        ApiPathInit apiPathInit = SpringUtil.getBean(ApiPathInit.class);
                        apiPathInit.init();
                        return true;
                    })
                    .onErrorResume(e -> {
                        log.error("Error building security context", e);
                        return Mono.just(false);
                    });
        } catch (Exception e) {
            log.error("Error parsing user info response", e);
            return Mono.just(false);
        }
    }

    // 构建安全上下文通用方法
    public static Mono<SecurityContext> buildSecurityContextFromUserInfo(IamUserInfo userInfo, String token, WebClient webClient, SecurityProperties securityProperties) {
        return Mono.fromCallable(() -> {
            SecurityContext context = new SecurityContext();

            // 设置用户主体信息
            SecurityContext.UserPrincipal principal = new SecurityContext.UserPrincipal();
            principal.setUserId(userInfo.getUid());
            principal.setUsername(userInfo.getCn());
            principal.setDisplayName(userInfo.getDisplayname());
            principal.setEmail(userInfo.getEmail());
            principal.setDeptId(userInfo.getDepartmentnumber());
            principal.setEmployeeNumber(userInfo.getEmployeenumber());
            principal.setPostType(userInfo.getPosttype());
            principal.setLocation(userInfo.getLocation());
            principal.setFilled(true);
            context.setPrincipal(principal);

            // 设置认证信息
            SecurityContext.Authentication authentication = new SecurityContext.Authentication();
            authentication.setAccessToken(token);
            authentication.setTokenType("Bearer");
            authentication.setAuthenticated(true);
            authentication.setAuthenticatedAt(LocalDateTime.now());
            context.setAuthentication(authentication);
            return context;
        }).flatMap(context ->
                loadUserPermissionsFromSystem(token, userInfo.getUid(), webClient, securityProperties)
                        .map(authorization -> {
                            context.setAuthorization(authorization);
                            return context;
                        })
        );
    }

    /**
     * 从远程权限接口加载用户权限信息
     */
    private Mono<SecurityContext.Authorization> loadUserPermissionsFromSystem(String token, String userId, WebClient webClient, SecurityProperties securityProperties) {
        try {
            log.debug("Loading user permissions from remote for user: {}", userId);

            // 修改URI构建方式，添加查询参数
            String uri = UriComponentsBuilder.fromUriString(securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTHORITY_PATH)
                    .queryParam("appId", securityProperties.getAppId())
                    .queryParam("username", userId)
                    .build().toUriString();
            return webClient.get()
                    .uri(uri)
                    .header(HttpHeaders.AUTHORIZATION, SecurityConstant.TOKEN_PREFIX + token)
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))  // 权限加载超时时间
                    .retryWhen(WebClientRetryUtils.createPermissionLoadRetry(2, Duration.ofMillis(300), uri, userId))
                    .map(body -> {
                        log.debug("Successfully loaded permissions for user: {}", userId);
                        JSONObject bodyObj = JSONObject.parseObject(body);
                        Integer code = bodyObj.getInteger("code");
                        String message = bodyObj.getString("msg");
                        JSONObject data = bodyObj.getJSONObject("data");
                        if (0 == code && data != null) {
                            List<SecurityContext.Role> roles = data.getObject("roles", new TypeReference<List<SecurityContext.Role>>() {
                            });

                            SecurityContext.Authorization authorization = new SecurityContext.Authorization();
                            if (CollectionUtils.isEmpty(roles)) {
                                authorization.setRoleCodes(List.of());
                                authorization.setRoles(List.of());
                            } else {
                                authorization.setRoleCodes(roles.stream().map(SecurityContext.Role::getCode).toList());
                                authorization.setRoles(roles);
                            }
                            return authorization;
                        } else {
                            log.warn("Failed to load permissions for user: {}, message: {}", userId, message);
                        }
                        return new SecurityContext.Authorization();
                    })
                    .doOnError(throwable -> WebClientRetryUtils.logConnectionError(throwable, uri, "permission loading", userId))
                    .onErrorResume(throwable -> {
                        log.error("Remote permission loading failed for user: {}, falling back to empty authorization", userId, throwable);
                        return Mono.just(new SecurityContext.Authorization());
                    });
        } catch (Exception e) {
            log.error("Error loading user permissions from remote for user: {}", userId, e);
        }
        return Mono.just(new SecurityContext.Authorization());
    }

    /**
     * 处理未授权错误
     *
     * @param securityProperties 安全属性
     * @param objectMapper       对象映射器
     * @param response           响应
     * @param message            消息
     * @return {@link Mono }<{@link Void }>
     */
    public static Mono<Void> handleUnauthorized(SecurityProperties securityProperties, ObjectMapper objectMapper, ServerHttpResponse response, String message) {
        String authorizeUrl = securityProperties.getEnvUrl() + SecurityConstant.AUTHORIZE_PATH +
                             securityProperties.getClientId() + SecurityConstant.REDIRECT_URI +
                             securityProperties.getRedirectUrl();
        return ErrorResponseUtils.writeUnauthorizedResponse(response, objectMapper, message, authorizeUrl);
    }

    /**
     * 处理权限不足错误
     *
     * @param objectMapper 对象映射器
     * @param response     响应
     * @param message      消息
     * @return {@link Mono }<{@link Void }>
     */
    public static Mono<Void> handleForbidden(ObjectMapper objectMapper, ServerHttpResponse response, String message) {
        return ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, message);
    }

    /**
     * 从请求参数或请求头中获取指定key的值（优先级：参数 > 头信息）
     * @param request 请求对象
     * @param key 要获取的键名
     * @return 匹配到的第一个值，未找到返回null
     */
    public static String getValueFromRequest(ServerHttpRequest request, String key) {
        // 优先从查询参数获取
        String paramValue = request.getQueryParams().getFirst(key);
        if (StringUtils.hasText(paramValue)) {
            return paramValue;
        }
        // 从请求头获取
        String headerValue = request.getHeaders().getFirst(key);
        if (StringUtils.hasText(headerValue)) {
            return headerValue;
        }

        return null;
    }
}
