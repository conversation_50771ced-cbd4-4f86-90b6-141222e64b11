package com.trinasolar.gateway.security.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.filter.AbstractSecurityFilter;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.service.WebFluxAuthenticationService;
import com.trinasolar.gateway.security.util.SecurityUtils;
import com.trinasolar.gateway.security.util.SecurityResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;

/**
 * 网关安全过滤器 - GlobalFilter实现
 * <p>
 * 基于统一认证服务的WebFlux响应式安全过滤器
 * 只处理认证，不处理API权限
 * <p>
 * 注意：此过滤器只能拦截通过Gateway路由配置的请求，
 * 无法拦截网关本身的RestController接口，
 * 需要配合 GatewayWebSecurityFilter 使用
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Slf4j
public class GatewaySecurityFilter implements GlobalFilter, Ordered {

    private final static String HEADER_USER_ID = "X-User-Id";
    private final static String HEADER_ORG_ID = "X-Org-Id";

    private final ObjectMapper objectMapper;
    private final AbstractSecurityFilter securityFilterHelper;

    public GatewaySecurityFilter(ObjectMapper objectMapper,
                               SecurityProperties securityProperties,
                               WebFluxAuthenticationService authenticationService) {
        this.objectMapper = objectMapper;
        this.securityFilterHelper = new AbstractSecurityFilter(securityProperties, authenticationService) {};
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();
        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        securityFilterHelper.logAuthenticationProcessing(path, authorization);

        // 1. 检查是否已经通过认证
        if (securityFilterHelper.isAlreadyAuthenticated()) {
            securityFilterHelper.logAuthenticationSkipped("already authenticated", path);
            return chain.filter(exchange);
        }

        // 2. 检查是否启用安全功能
        if (!securityFilterHelper.isSecurityEnabled()) {
            securityFilterHelper.logAuthenticationSkipped("security disabled", path);
            return chain.filter(exchange);
        }

        // 3. 默认白名单处理
        if (securityFilterHelper.matchDefaultWhiteList(path, null)) {
            securityFilterHelper.logAuthenticationSkipped("default whitelist", path);
            return chain.filter(exchange);
        }

        // 4. 配置白名单处理
        if (securityFilterHelper.matchConfiguredWhiteList(path, null)) {
            securityFilterHelper.logAuthenticationSkipped("configured whitelist", path);
            return chain.filter(exchange);
        }

        // 5. 内部系统签名验证
        String tsltoken = SecurityUtils.getValueFromRequest(request, "tsltoken");
        if (securityFilterHelper.matchInternalSystemSignature(tsltoken)) {
            securityFilterHelper.logAuthenticationSkipped("internal system signature", path);
            return chain.filter(exchange);
        }

        // 6. 检查Authorization头
        if (!securityFilterHelper.isValidAuthorizationHeader(authorization)) {
            log.warn("Missing or invalid authorization header for path: {}", path);
            return SecurityResponseUtils.handleUnauthorized(securityFilterHelper.securityProperties, objectMapper, response, "token 为空");
        }

        // 7. 验证token并设置安全上下文
        return ((WebFluxAuthenticationService)securityFilterHelper.authenticationService).verifyTokenAndSetContextAsync(authorization)
                .flatMap(isValid -> {
                    if (isValid) {
                        securityFilterHelper.logAuthenticationSuccess(path);
                        // 新增代码：设置请求头和Cookie
                        ServerHttpRequest newRequest = addDownstreamHeaders(request);


                        // 设置顶级域名Cookie（示例域名.trinasolar.com，按实际修改）
                        ResponseCookie cookie = ResponseCookie.from("token", authorization.substring(SecurityConstant.TOKEN_PREFIX.length()).trim())
                                .domain(".trinasolar.com")
                                .path("/")
                                .httpOnly(true)
                                .secure(true)
                                .maxAge(Duration.ofDays(7))
                                .build();
                        response.addCookie(cookie);

                        return chain.filter(exchange.mutate().request(newRequest).response(response).build());
                    } else {
                        securityFilterHelper.logAuthenticationFailure(path);
                        return SecurityResponseUtils.handleUnauthorized(securityFilterHelper.securityProperties, objectMapper, response, "token 无效，验证失败");
                    }
                })
                .onErrorResume(throwable -> {
                    securityFilterHelper.logAuthenticationError(path, throwable);
                    return SecurityResponseUtils.handleUnauthorized(securityFilterHelper.securityProperties, objectMapper, response, "token 验证异常");
                });
    }

    // 添加新方法：构建下游请求头
    private ServerHttpRequest addDownstreamHeaders(ServerHttpRequest request) {
        return request.mutate()
                .header(HEADER_USER_ID, SecurityContextHolder.getCurrentUserId())
                .header(HttpHeaders.AUTHORIZATION, request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION))
                .build();
    }


    @Override
    public int getOrder() {
        // 设置比WebFilter稍低的优先级，避免重复处理
        return Ordered.HIGHEST_PRECEDENCE + 2;
    }
}
