package com.trinasolar.common.security.filter;

import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.AuthenticationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 抽象安全过滤器基类
 * <p>
 * 提供通用的安全检查逻辑，避免重复代码
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
public abstract class AbstractSecurityFilter {

    /**
     * 默认白名单，注意为包含contextPath的路径
     */
    public static final List<String> DEFAULT_WHITE_URLS = List.of(
            "/api/scf/token",
            "/swagger-ui/**", "/swagger-resources/**", "/webjars/**", "/v3/api-docs/**", "/v2/api-docs/**",
            "favicon.ico", "/error", "/actuator/**", "health"
    );

    /**
     * 内部系统签名token
     */
    protected static final String INTERNAL_SYSTEM_TOKEN = "hF7jR6sK6zE9yP3bI3iN3fQ9wU1vI5cR";

    public final SecurityProperties securityProperties;
    public final AuthenticationService authenticationService;

    protected AbstractSecurityFilter(SecurityProperties securityProperties, 
                                   AuthenticationService authenticationService) {
        this.securityProperties = securityProperties;
        this.authenticationService = authenticationService;
    }

    /**
     * 检查是否已经通过认证
     */
    public boolean isAlreadyAuthenticated() {
        return SecurityContextHolder.getContext() != null &&
                SecurityContextHolder.getContext().getAuthentication() != null &&
                SecurityContextHolder.getContext().getAuthentication().isAuthenticated();
    }

    /**
     * 检查是否启用安全功能
     */
    public boolean isSecurityEnabled() {
        return securityProperties.isEnabled();
    }

    /**
     * 检查默认白名单
     */
    public boolean matchDefaultWhiteList(String path, String contextPath) {
        return authenticationService.matchWhiteUrl(DEFAULT_WHITE_URLS, path, contextPath);
    }

    /**
     * 检查配置白名单
     */
    public boolean matchConfiguredWhiteList(String path, String contextPath) {
        List<String> whiteUrls = securityProperties.getWhiteUris();
        return authenticationService.matchWhiteUrl(whiteUrls, path, contextPath);
    }

    /**
     * 检查内部系统签名
     */
    public boolean matchInternalSystemSignature(String tsltoken) {
        return StringUtils.hasText(tsltoken) && INTERNAL_SYSTEM_TOKEN.equals(tsltoken);
    }

    /**
     * 验证Authorization头格式
     */
    public boolean isValidAuthorizationHeader(String authorization) {
        return StringUtils.hasText(authorization) && authorization.startsWith(SecurityConstant.TOKEN_PREFIX);
    }

    /**
     * 记录认证跳过日志
     */
    public void logAuthenticationSkipped(String reason, String path) {
        log.debug("Request path {} skipping authentication: {}", path, reason);
    }

    /**
     * 记录认证处理日志
     */
    public void logAuthenticationProcessing(String path, String authorization) {
        log.debug("Security filter processing request: {} with authorization: {}", path,
                authorization != null ? "Bearer ***" : "null");
    }

    /**
     * 记录认证成功日志
     */
    public void logAuthenticationSuccess(String path) {
        log.debug("Token validation successful for path: {}", path);
    }

    /**
     * 记录认证失败日志
     */
    public void logAuthenticationFailure(String path) {
        log.warn("Token validation failed for path: {}", path);
    }

    /**
     * 记录认证异常日志
     */
    public void logAuthenticationError(String path, Throwable throwable) {
        log.error("Error during token validation for path: {}", path, throwable);
    }
}
