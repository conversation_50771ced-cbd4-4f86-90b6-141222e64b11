package com.trinasolar.common.security.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.filter.AbstractSecurityFilter;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.AuthenticationService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import jakarta.servlet.Filter;

import java.io.IOException;
import java.util.HashMap;

/**
 * 安全拦截器
 * <p>
 * 基于统一认证服务的Servlet环境实现
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Component
public class SecurityInterceptor extends OncePerRequestFilter {

    private final ObjectMapper objectMapper;
    private final AbstractSecurityFilter securityFilterHelper;

    public SecurityInterceptor(ObjectMapper objectMapper,
                             SecurityProperties securityProperties,
                             AuthenticationService authenticationService) {
        this.objectMapper = objectMapper;
        this.securityFilterHelper = new AbstractSecurityFilter(securityProperties, authenticationService) {};
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String path = request.getRequestURI();
        String contextPath = request.getContextPath();
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);

        try {
            securityFilterHelper.logAuthenticationProcessing(path, authorization);

            // 1. 检查是否已经通过认证
            if (securityFilterHelper.isAlreadyAuthenticated()) {
                securityFilterHelper.logAuthenticationSkipped("already authenticated", path);
                filterChain.doFilter(request, response);
                return;
            }

            // 2. 检查是否启用安全功能
            if (!securityFilterHelper.isSecurityEnabled()) {
                securityFilterHelper.logAuthenticationSkipped("security disabled", path);
                filterChain.doFilter(request, response);
                return;
            }

            // 3. 默认白名单处理
            if (securityFilterHelper.matchDefaultWhiteList(path, contextPath)) {
                securityFilterHelper.logAuthenticationSkipped("default whitelist", path);
                filterChain.doFilter(request, response);
                return;
            }

            // 4. 配置白名单处理
            if (securityFilterHelper.matchConfiguredWhiteList(path, contextPath)) {
                securityFilterHelper.logAuthenticationSkipped("configured whitelist", path);
                filterChain.doFilter(request, response);
                return;
            }

            // 5. 验证Authorization头
            if (!securityFilterHelper.isValidAuthorizationHeader(authorization)) {
                writeErrorMessage(response, "token 为空");
                return;
            }

            // 6. 验证token
            if (!securityFilterHelper.authenticationService.verifyTokenAndSetContext(authorization)) {
                securityFilterHelper.logAuthenticationFailure(path);
                writeErrorMessage(response, "token 无效，验证失败");
                return;
            }

            securityFilterHelper.logAuthenticationSuccess(path);
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            securityFilterHelper.logAuthenticationError(path, e);
            writeErrorMessage(response, "token 验证异常");
        }
    }




    /**
     * 写入错误消息
     *
     * @param response 响应
     * @param message  消息
     * @throws IOException IOException
     */
    private void writeErrorMessage(HttpServletResponse response, String message) throws IOException {
        String authorizeUrl = securityFilterHelper.securityProperties.getEnvUrl() + SecurityConstant.AUTHORIZE_PATH +
                             securityFilterHelper.securityProperties.getClientId() + SecurityConstant.REDIRECT_URI +
                             securityFilterHelper.securityProperties.getRedirectUrl();
        HashMap<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", message);
        result.put("data", authorizeUrl);

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(result));
        response.getWriter().flush();
    }
}
