package com.trinasolar.common.security.annotation;


import com.trinasolar.common.security.AutoSecurityConfiguration;
import com.trinasolar.common.security.cache.IamCaffeineCacheConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用安全管控
 *
 * <AUTHOR>
 * @date 2025-06-20 13:53
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({IamCaffeineCacheConfiguration.class, AutoSecurityConfiguration.class})
public @interface EnableSecurity {
}
