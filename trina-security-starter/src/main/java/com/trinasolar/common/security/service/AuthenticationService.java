package com.trinasolar.common.security.service;

import com.trinasolar.common.security.domain.SecurityContext;

import java.util.List;

/**
 * 统一认证服务接口
 * <p>
 * 提供servlet和webflux环境下的统一认证抽象
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface AuthenticationService {

    /**
     * 检查路径是否在白名单中
     *
     * @param whiteUrls 白名单列表
     * @param path 请求路径
     * @param contextPath 上下文路径(servlet环境使用，webflux可为null)
     * @return 是否匹配白名单
     */
    boolean matchWhiteUrl(List<String> whiteUrls, String path, String contextPath);

    /**
     * 验证token并构建SecurityContext (同步版本 - servlet环境)
     *
     * @param authorization Authorization头
     * @return 验证是否成功
     */
    boolean verifyTokenAndSetContext(String authorization);

    /**
     * 构建SecurityContext
     *
     * @param userInfoJson 用户信息JSON
     * @param token token字符串
     * @return SecurityContext
     */
    SecurityContext buildSecurityContext(String userInfoJson, String token);
}
