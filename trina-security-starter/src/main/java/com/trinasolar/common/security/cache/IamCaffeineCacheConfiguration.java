package com.trinasolar.common.security.cache;


import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.trinasolar.common.security.properties.IamCaffeineCacheProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ForkJoinPool;

/**
 * Caffeine缓存生产环境最佳实践配置
 * <p>
 * 生产环境配置要点：
 * 1. 合理设置缓存容量，避免OOM
 * 2. 配置适当的过期策略
 * 3. 启用统计监控
 * 4. 使用异步监听器避免阻塞
 * 5. 配置合适的刷新策略
 * 6. 设置弱引用避免内存泄漏
 *
 * <AUTHOR>
 * @date 2025-06-20 16:20
 */
@Slf4j
@RequiredArgsConstructor
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(IamCaffeineCacheProperties.class)
public class IamCaffeineCacheConfiguration {

    private final IamCaffeineCacheProperties iamCaffeineCacheProperties;

    /**
     * IAM 本地缓存配置 - 生产环境优化版本
     * <p>
     * 适用场景：认证鉴权缓存，高频访问，对一致性要求不高
     * <p>
     * 配置说明：
     * - initialCapacity: 初始容量，设置为预估并发用户数
     * - maximumSize: 最大缓存条目数，防止内存溢出
     * - expireAfterWrite: 写入后过期时间，保证数据时效性
     * - expireAfterAccess: 访问后过期时间，清理冷数据
     * - weakKeys: 使用弱引用key，防止内存泄漏
     * - recordStats: 开启统计，便于监控
     * - scheduler: 使用系统调度器，及时清理过期数据
     *
     * @return {@link Cache }<{@link String }, {@link Object }>
     */
    @Bean("iamCache")
    public Cache<String, Object> iamCache() {
        log.info("Initializing IAM Caffeine cache with config: {}", iamCaffeineCacheProperties);

        return Caffeine.newBuilder()
                // 初始容量：根据预估的并发用户数设置，避免频繁扩容
                .initialCapacity(iamCaffeineCacheProperties.getInitialCapacity())
                // 最大缓存数量：防止内存溢出，建议不超过JVM堆内存的5%
                .maximumSize(iamCaffeineCacheProperties.getMaximumSize())
                // 写入后过期：保证数据时效性，认证信息建议5-10分钟
                .expireAfterWrite(iamCaffeineCacheProperties.getExpireAfterWrite())
                // 访问后过期：清理长时间未访问的数据，节省内存
                .expireAfterAccess(iamCaffeineCacheProperties.getExpireAfterAccess())
                // 注释掉weakKeys()，避免key被GC导致缓存失效
                // 对于token缓存，使用强引用key更合适，因为有明确的过期时间和大小限制
                // .weakKeys()
                // 使用系统调度器，确保过期数据及时清理
                .scheduler(Scheduler.systemScheduler())
                // 异步移除监听器，避免阻塞缓存操作
                .removalListener(this::handleCacheRemoval)
                // 开启统计功能，便于监控缓存性能
                .recordStats()
                .build();
    }

    /**
     * 异步处理缓存移除事件
     * 避免在缓存操作线程中执行耗时操作
     */
    private void handleCacheRemoval(String key, Object value, RemovalCause cause) {
        // 使用ForkJoinPool异步处理，避免阻塞缓存操作
        ForkJoinPool.commonPool().execute(() -> {
            try {
                if (log.isDebugEnabled()) {
                    log.debug("Cache entry removed - key: [{}], cause: [{}], valueType: [{}]",
                            key, cause, value != null ? value.getClass().getSimpleName() : "null");
                }

                // 根据移除原因进行不同处理
                switch (cause) {
                    case EXPIRED:
                        // 过期移除：可以记录统计信息
                        log.trace("Cache entry expired: {}", key);
                        break;
                    case SIZE:
                        // 容量限制移除：可能需要调整缓存大小
                        log.warn("Cache entry evicted due to size limit: {}", key);
                        break;
                    case COLLECTED:
                        // GC回收：弱引用被回收（注意：现在已移除weakKeys配置）
                        log.warn("Cache entry collected by GC (unexpected): {}", key);
                        break;
                    default:
                        log.trace("Cache entry removed: {} - {}", key, cause);
                }
            } catch (Exception e) {
                log.error("Error handling cache removal for key: {}", key, e);
            }
        });
    }

    /**
     * 获取缓存统计信息（用于调试和监控）
     */
    @Bean
    public CacheStatsLogger cacheStatsLogger(Cache<String, Object> iamCache) {
        return new CacheStatsLogger(iamCache);
    }

    /**
     * 缓存统计日志记录器
     */
    public static class CacheStatsLogger {
        private final Cache<String, Object> cache;

        public CacheStatsLogger(Cache<String, Object> cache) {
            this.cache = cache;
        }

        public void logCacheStats() {
            if (log.isDebugEnabled()) {
                var stats = cache.stats();
                log.debug("Cache Stats - Size: {}, Hit Rate: {:.2f}%, Miss Rate: {:.2f}%, Evictions: {}",
                        cache.estimatedSize(),
                        stats.hitRate() * 100,
                        stats.missRate() * 100,
                        stats.evictionCount());
            }
        }

        public Cache<String, Object> getCache() {
            return cache;
        }
    }
}
