package com.trinasolar.common.security.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.AuthenticationService;
import com.trinasolar.common.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Servlet环境认证服务实现
 * <p>
 * 基于RestTemplate的同步实现
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServletAuthenticationServiceImpl implements AuthenticationService {

    private final ObjectMapper objectMapper;
    private final SecurityProperties securityProperties;
    private final RestTemplate restTemplate;
    private final Cache<String, Object> iamCache;
    private final PermissionService permissionService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean matchWhiteUrl(List<String> whiteUrls, String path, String contextPath) {
        if (whiteUrls == null || whiteUrls.isEmpty()) {
            return false;
        }

        for (String whiteUrl : whiteUrls) {
            // 处理contextPath
            String fullPath = StringUtils.hasText(contextPath) ? contextPath + path : path;
            
            if (pathMatcher.match(whiteUrl, fullPath) || pathMatcher.match(whiteUrl, path)) {
                log.debug("Request path {} matches whitelist pattern: {}", path, whiteUrl);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean verifyTokenAndSetContext(String authorization) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查token缓存
            Object cachedResult = iamCache.getIfPresent(tokenCacheKey);
            log.debug("Token cache lookup - key: {}, found: {}, cache size: {}",
                    tokenCacheKey, cachedResult != null, iamCache.estimatedSize());

            if (cachedResult != null) {
                log.debug("Token found in cache, validation successful");
                return true;
            }

            // 缓存未命中，远程验证token
            log.debug("Token not found in cache, performing remote validation");
            return verifyTokenRemotelyAndSetContext(authorization);

        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return false;
        }
    }

    @Override
    public SecurityContext buildSecurityContext(String userInfoJson, String token) {
        try {
            IamUserInfo userInfo = objectMapper.readValue(userInfoJson, IamUserInfo.class);
            return buildSecurityContextFromUserInfo(userInfo, token);
        } catch (Exception e) {
            log.error("Error building security context from user info", e);
            throw new RuntimeException("Failed to build security context", e);
        }
    }

    /**
     * 远程验证token并设置安全上下文
     */
    private boolean verifyTokenRemotelyAndSetContext(String authorization) {
        try {
            String url = securityProperties.getEnvUrl() + SecurityConstant.USERINFO_PATH;

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, authorization);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String body = response.getBody();
                IamUserInfo userInfo = objectMapper.readValue(body, IamUserInfo.class);
                log.debug("Remote get userInfo: {}", userInfo);

                // 构建安全上下文
                String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
                SecurityContext securityContext = buildSecurityContextFromUserInfo(userInfo, token);

                // 填充权限信息
                fillPermissionsToContext(securityContext, authorization);

                String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);
                iamCache.put(tokenCacheKey, token);
                log.debug("Token cached successfully - key: {}, cache size: {}",
                        tokenCacheKey, iamCache.estimatedSize());
                
                // 设置到ThreadLocal
                SecurityContextHolder.setContext(securityContext);
                return true;
            } else {
                log.warn("Remote token validation failed with status: {}", response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("Error during remote token validation", e);
            return false;
        }
    }

    /**
     * 构建安全上下文（从IamUserInfo）
     */
    private SecurityContext buildSecurityContextFromUserInfo(IamUserInfo userInfo, String token) {
        SecurityContext context = new SecurityContext();

        // 设置用户主体信息
        SecurityContext.UserPrincipal principal = new SecurityContext.UserPrincipal();
        principal.setUserId(userInfo.getUid());
        principal.setUsername(userInfo.getCn());
        principal.setDisplayName(userInfo.getDisplayname());
        principal.setEmail(userInfo.getEmail());
        principal.setDeptId(userInfo.getDepartmentnumber());
        principal.setEmployeeNumber(userInfo.getEmployeenumber());
        principal.setPostType(userInfo.getPosttype());
        principal.setLocation(userInfo.getLocation());
        principal.setFilled(true);
        context.setPrincipal(principal);

        // 设置认证信息
        SecurityContext.Authentication authentication = new SecurityContext.Authentication();
        authentication.setAccessToken(token);
        authentication.setTokenType("Bearer");
        authentication.setAuthenticated(true);
        authentication.setAuthenticatedAt(LocalDateTime.now());
        context.setAuthentication(authentication);

        return context;
    }

    /**
     * 填充权限信息到安全上下文
     */
    private void fillPermissionsToContext(SecurityContext context, String token) {
        if (context.getPrincipal() == null || context.getPrincipal().getUserId() == null) {
            return;
        }

        String userId = context.getPrincipal().getUserId();

        // 设置授权信息
        SecurityContext.Authorization authorization = permissionService.getUserPermissions(token, userId);
        authorization.setPermissionsLoadedAt(LocalDateTime.now());
        context.setAuthorization(authorization);

        log.debug("Permissions loaded for user: {} - roles: {}, permissions: {}",
                userId, authorization.getRoleCodes().size(), authorization.getPermissions().size());
    }
}
